2025-08-19T23:04:44.746+08:00  INFO 26092 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Starting McpServerComputerApplication v1.0.0 using Java 17.0.9 with PID 26092 (E:\MavenRepository\cn\bugstack\mcp\mcp-server-computer\1.0.0\mcp-server-computer-1.0.0.jar started by 90789 in E:\IdeaPro\XFG\ai-mcp-knowledge)
2025-08-19T23:04:44.753+08:00  INFO 26092 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-19T23:04:45.385+08:00  INFO 26092 --- [mcp-server-computer] [main] o.s.a.a.m.s.MpcServerAutoConfiguration   : Registered tools1 notification: true
2025-08-19T23:04:45.507+08:00  INFO 26092 --- [mcp-server-computer] [pool-2-thread-1] i.m.server.McpAsyncServer                : Client initialize request - Protocol: 2024-11-05, Capabilities: ClientCapabilities[experimental=null, roots=null, sampling=null], Info: Implementation[name=spring-ai-mcp-client, version=1.0.0]
2025-08-19T23:04:45.600+08:00  INFO 26092 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Started McpServerComputerApplication in 1.23 seconds (process running for 1.561)
2025-08-19T23:04:45.602+08:00  INFO 26092 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : mcp server computer success!
2025-08-19T23:09:30.080+08:00  INFO 21824 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Starting McpServerComputerApplication v1.0.0 using Java 17.0.9 with PID 21824 (E:\MavenRepository\cn\bugstack\mcp\mcp-server-computer\1.0.0\mcp-server-computer-1.0.0.jar started by 90789 in E:\IdeaPro\XFG\ai-mcp-knowledge)
2025-08-19T23:09:30.082+08:00  INFO 21824 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-19T23:09:30.762+08:00  INFO 21824 --- [mcp-server-computer] [main] o.s.a.a.m.s.MpcServerAutoConfiguration   : Registered tools1 notification: true
2025-08-19T23:09:30.898+08:00  INFO 21824 --- [mcp-server-computer] [pool-2-thread-1] i.m.server.McpAsyncServer                : Client initialize request - Protocol: 2024-11-05, Capabilities: ClientCapabilities[experimental=null, roots=null, sampling=null], Info: Implementation[name=spring-ai-mcp-client, version=1.0.0]
2025-08-19T23:09:31.001+08:00  INFO 21824 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Started McpServerComputerApplication in 1.278 seconds (process running for 1.602)
2025-08-19T23:09:31.004+08:00  INFO 21824 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : mcp server computer success!
2025-08-19T23:14:27.897+08:00  INFO 5288 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Starting McpServerComputerApplication v1.0.0 using Java 17.0.9 with PID 5288 (E:\MavenRepository\cn\bugstack\mcp\mcp-server-computer\1.0.0\mcp-server-computer-1.0.0.jar started by 90789 in E:\IdeaPro\XFG\ai-mcp-knowledge)
2025-08-19T23:14:27.901+08:00  INFO 5288 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-19T23:14:28.627+08:00  INFO 5288 --- [mcp-server-computer] [main] o.s.a.a.m.s.MpcServerAutoConfiguration   : Registered tools1 notification: true
2025-08-19T23:14:28.758+08:00  INFO 5288 --- [mcp-server-computer] [pool-2-thread-1] i.m.server.McpAsyncServer                : Client initialize request - Protocol: 2024-11-05, Capabilities: ClientCapabilities[experimental=null, roots=null, sampling=null], Info: Implementation[name=spring-ai-mcp-client, version=1.0.0]
2025-08-19T23:14:28.850+08:00  INFO 5288 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Started McpServerComputerApplication in 1.336 seconds (process running for 1.667)
2025-08-19T23:14:28.853+08:00  INFO 5288 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : mcp server computer success!

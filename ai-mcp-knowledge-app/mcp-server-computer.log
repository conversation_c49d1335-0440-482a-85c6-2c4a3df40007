2025-08-21T21:28:21.615+08:00  INFO 9824 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Starting McpServerComputerApplication v1.0.0 using Java 17.0.9 with PID 9824 (E:\MavenRepository\cn\bugstack\mcp\mcp-server-computer\1.0.0\mcp-server-computer-1.0.0.jar started by 90789 in E:\IdeaPro\XFG\ai-mcp-knowledge\ai-mcp-knowledge-app)
2025-08-21T21:28:21.622+08:00  INFO 9824 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-21T21:28:22.364+08:00  INFO 9824 --- [mcp-server-computer] [main] o.s.a.a.m.s.MpcServerAutoConfiguration   : Registered tools1 notification: true
2025-08-21T21:28:22.490+08:00  INFO 9824 --- [mcp-server-computer] [pool-2-thread-1] i.m.server.McpAsyncServer                : Client initialize request - Protocol: 2024-11-05, Capabilities: ClientCapabilities[experimental=null, roots=null, sampling=null], Info: Implementation[name=spring-ai-mcp-client, version=1.0.0]
2025-08-21T21:28:22.591+08:00  INFO 9824 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Started McpServerComputerApplication in 1.391 seconds (process running for 1.778)
2025-08-21T21:28:22.593+08:00  INFO 9824 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : mcp server computer success!
2025-08-21T21:29:59.991+08:00  INFO 15008 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Starting McpServerComputerApplication v1.0.0 using Java 17.0.9 with PID 15008 (E:\MavenRepository\cn\bugstack\mcp\mcp-server-computer\1.0.0\mcp-server-computer-1.0.0.jar started by 90789 in E:\IdeaPro\XFG\ai-mcp-knowledge\ai-mcp-knowledge-app)
2025-08-21T21:29:59.994+08:00  INFO 15008 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-21T21:30:00.689+08:00  INFO 15008 --- [mcp-server-computer] [main] o.s.a.a.m.s.MpcServerAutoConfiguration   : Registered tools1 notification: true
2025-08-21T21:30:00.811+08:00  INFO 15008 --- [mcp-server-computer] [pool-2-thread-1] i.m.server.McpAsyncServer                : Client initialize request - Protocol: 2024-11-05, Capabilities: ClientCapabilities[experimental=null, roots=null, sampling=null], Info: Implementation[name=spring-ai-mcp-client, version=1.0.0]
2025-08-21T21:30:00.906+08:00  INFO 15008 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Started McpServerComputerApplication in 1.324 seconds (process running for 1.651)
2025-08-21T21:30:00.909+08:00  INFO 15008 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : mcp server computer success!
2025-08-21T21:31:46.861+08:00  INFO 11940 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Starting McpServerComputerApplication v1.0.0 using Java 17.0.9 with PID 11940 (E:\MavenRepository\cn\bugstack\mcp\mcp-server-computer\1.0.0\mcp-server-computer-1.0.0.jar started by 90789 in E:\IdeaPro\XFG\ai-mcp-knowledge\ai-mcp-knowledge-app)
2025-08-21T21:31:46.864+08:00  INFO 11940 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-21T21:31:47.509+08:00  INFO 11940 --- [mcp-server-computer] [main] o.s.a.a.m.s.MpcServerAutoConfiguration   : Registered tools1 notification: true
2025-08-21T21:31:47.634+08:00  INFO 11940 --- [mcp-server-computer] [pool-2-thread-1] i.m.server.McpAsyncServer                : Client initialize request - Protocol: 2024-11-05, Capabilities: ClientCapabilities[experimental=null, roots=null, sampling=null], Info: Implementation[name=spring-ai-mcp-client, version=1.0.0]
2025-08-21T21:31:47.730+08:00  INFO 11940 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Started McpServerComputerApplication in 1.233 seconds (process running for 1.595)
2025-08-21T21:31:47.733+08:00  INFO 11940 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : mcp server computer success!
2025-08-21T21:32:21.832+08:00  INFO 7092 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Starting McpServerComputerApplication v1.0.0 using Java 17.0.9 with PID 7092 (E:\MavenRepository\cn\bugstack\mcp\mcp-server-computer\1.0.0\mcp-server-computer-1.0.0.jar started by 90789 in E:\IdeaPro\XFG\ai-mcp-knowledge\ai-mcp-knowledge-app)
2025-08-21T21:32:21.835+08:00  INFO 7092 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-21T21:32:22.584+08:00  INFO 7092 --- [mcp-server-computer] [main] o.s.a.a.m.s.MpcServerAutoConfiguration   : Registered tools1 notification: true
2025-08-21T21:32:22.710+08:00  INFO 7092 --- [mcp-server-computer] [pool-2-thread-1] i.m.server.McpAsyncServer                : Client initialize request - Protocol: 2024-11-05, Capabilities: ClientCapabilities[experimental=null, roots=null, sampling=null], Info: Implementation[name=spring-ai-mcp-client, version=1.0.0]
2025-08-21T21:32:22.822+08:00  INFO 7092 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Started McpServerComputerApplication in 1.416 seconds (process running for 1.769)
2025-08-21T21:32:22.825+08:00  INFO 7092 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : mcp server computer success!
2025-08-21T21:32:29.005+08:00  INFO 7092 --- [mcp-server-computer] [boundedElastic-1] c.b.m.s.c.d.service.ComputerService      : 获取电脑配置信息 D:\test
2025-08-21T21:35:42.897+08:00  INFO 30180 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Starting McpServerComputerApplication v1.0.0 using Java 17.0.9 with PID 30180 (E:\MavenRepository\cn\bugstack\mcp\mcp-server-computer\1.0.0\mcp-server-computer-1.0.0.jar started by 90789 in E:\IdeaPro\XFG\ai-mcp-knowledge\ai-mcp-knowledge-app)
2025-08-21T21:35:42.899+08:00  INFO 30180 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-21T21:35:43.606+08:00  INFO 30180 --- [mcp-server-computer] [main] o.s.a.a.m.s.MpcServerAutoConfiguration   : Registered tools1 notification: true
2025-08-21T21:35:43.737+08:00  INFO 30180 --- [mcp-server-computer] [pool-2-thread-1] i.m.server.McpAsyncServer                : Client initialize request - Protocol: 2024-11-05, Capabilities: ClientCapabilities[experimental=null, roots=null, sampling=null], Info: Implementation[name=spring-ai-mcp-client, version=1.0.0]
2025-08-21T21:35:43.831+08:00  INFO 30180 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Started McpServerComputerApplication in 1.309 seconds (process running for 1.662)
2025-08-21T21:35:43.834+08:00  INFO 30180 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : mcp server computer success!
